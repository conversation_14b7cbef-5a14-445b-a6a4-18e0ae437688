View members popup doesnt work correctly. It always show all participants in the hub and not the channel members

I dont like the channel options button. Lets make it a dropdown with details, which will open the same dialog as now, another option with manage participants and another option with delete channel. We can remove the partiicpant and elet echannel from the dialog.
Also I want when I add members, to see it as message in the channel, this will require BE changes. look into it.

when I edit a post who has review with needs rework, show me prompt which will ask me if I want to send notifications to reviewer who said needs rework.
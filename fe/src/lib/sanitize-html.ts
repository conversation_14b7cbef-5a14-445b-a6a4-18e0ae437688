import DOMPurify from 'dompurify'

/**
 * Sanitize HTML for safe storage/rendering.
 * Keeps only basic formatting, lists, headings and links that we explicitly allow.
 * This should be used both on-change (editor) and on-submit as a final safeguard.
 */
export function sanitizeHtml(input: string): string {
  if (!input) return ''

  // Configure allowed tags/attributes conservatively
  const allowedTags = [
    'p', 'br', 'strong', 'b', 'em', 'i', 'u',
    'ul', 'ol', 'li',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'a'
  ] as const

  // Use DOMPurify to sanitize
  const clean = DOMPurify.sanitize(input, {
    ALLOWED_TAGS: allowedTags as unknown as string[],
    ALLOWED_ATTR: ['href', 'target', 'rel'],
    FORBID_TAGS: ['script', 'style', 'iframe', 'object', 'embed'],
    // Prevent JavaScript URLs in links
    ALLOW_UNKNOWN_PROTOCOLS: false,
    USE_PROFILES: { html: true },
    ADD_ATTR: ['target', 'rel']
  })

  return typeof clean === 'string' ? clean : ''
}

/** Ensure external links are safe (rel, target) */
export function normalizeLinkAttrs(html: string): string {
  if (!html) return ''
  // Add rel and target to anchors without them (light-weight approach)
  return html.replace(/<a(\s+[^>]*href=["'][^"']+["'][^>]*)>/gi, (m) => {
    let updated = m
    if (!/\brel=/.test(updated)) updated = updated.replace(/>$/, ' rel="noopener noreferrer">')
    if (!/\btarget=/.test(updated)) updated = updated.replace(/>$/, ' target="_blank">')
    return updated
  })
}


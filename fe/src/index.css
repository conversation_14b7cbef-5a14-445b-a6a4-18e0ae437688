@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
    --radius: 0.625rem;
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Mention token styles for dual-state mention system */
  .mention-token {
    @apply inline-block text-blue-600 bg-blue-50 px-2 py-1 rounded-md text-sm font-medium cursor-pointer;
    @apply dark:text-blue-400 dark:bg-blue-950/50;
    @apply hover:bg-blue-100 dark:hover:bg-blue-900/50;
    @apply transition-all duration-150;
    @apply border border-blue-200 dark:border-blue-800;
    user-select: none;
    white-space: nowrap;
    direction: ltr;
    unicode-bidi: embed;
    margin: 0 1px;
    box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
  }

  /* Enhanced hover and focus states */
  .mention-token:hover {
    @apply bg-blue-100 dark:bg-blue-900/60;
    @apply border-blue-300 dark:border-blue-700;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
  }

  /* Active state for better feedback */
  .mention-token:active {
    @apply bg-blue-200 dark:bg-blue-900/70;
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(59, 130, 246, 0.2);
  }

  /* Ensure mention tokens don't break across lines */
  .mention-token:before,
  .mention-token:after {
    content: '';
    white-space: nowrap;
  }

  /* Better visual separation in contenteditable */
  [contenteditable] .mention-token {
    @apply mx-0.5;
  }

  /* Prevent text selection issues */
  .mention-token * {
    user-select: none;
    pointer-events: none;
  }

  /* Rich Text Editor Prose Styles - Custom implementation for shadcn UI compatibility */
  .prose {
    color: var(--foreground);
    max-width: none;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    color: var(--foreground);
    font-weight: 600;
    line-height: 1.25;
  }

  .prose h1 {
    font-size: 2rem;
    margin-top: 0;
    margin-bottom: 1rem;
  }

  .prose h2 {
    font-size: 1.5rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .prose h3 {
    font-size: 1.25rem;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .prose h4 {
    font-size: 1.125rem;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .prose h5 {
    font-size: 1rem;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .prose h6 {
    font-size: 0.875rem;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .prose p {
    margin-top: 1rem;
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  .prose ul,
  .prose ol {
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  /* Unordered list styling with bullet points */
  .prose ul {
    list-style-type: disc;
  }

  /* Ordered list styling with numbers */
  .prose ol {
    list-style-type: decimal;
  }

  .prose ul > li,
  .prose ol > li {
    position: relative;
    padding-left: 0.25rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    display: list-item;
  }

  /* Nested list styling */
  .prose ul ul {
    list-style-type: circle;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .prose ul ul ul {
    list-style-type: square;
  }

  .prose ol ol {
    list-style-type: lower-alpha;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .prose ol ol ol {
    list-style-type: lower-roman;
  }

  .prose li p {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  /* Ensure list markers are visible and properly colored */
  .prose ul > li::marker,
  .prose ol > li::marker {
    color: var(--muted-foreground);
  }

  /* Fix for TipTap editor - ensure list items display correctly */
  .prose .ProseMirror ul,
  .prose .ProseMirror ol {
    list-style-position: outside;
  }

  .prose .ProseMirror ul > li,
  .prose .ProseMirror ol > li {
    display: list-item;
    list-style-position: outside;
  }

  .prose a {
    color: var(--primary);
    text-decoration: underline;
    text-underline-offset: 2px;
  }

  .prose a:hover {
    text-decoration: none;
  }

  .prose strong {
    color: var(--foreground);
    font-weight: 600;
  }

  .prose > :first-child {
    margin-top: 0;
  }

  .prose > :last-child {
    margin-bottom: 0;
  }

  /* Prose small variant */
  .prose-sm {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .prose-sm h1 {
    font-size: 1.75rem;
  }

  .prose-sm h2 {
    font-size: 1.25rem;
  }

  .prose-sm h3 {
    font-size: 1.125rem;
  }

  .prose-sm h4 {
    font-size: 1rem;
  }

  .prose-sm h5 {
    font-size: 0.875rem;
  }

  .prose-sm h6 {
    font-size: 0.75rem;
  }

  /* Dark mode prose styles */
  .dark .prose {
    color: var(--foreground);
  }

  .dark .prose h1,
  .dark .prose h2,
  .dark .prose h3,
  .dark .prose h4,
  .dark .prose h5,
  .dark .prose h6 {
    color: var(--foreground);
  }

  .dark .prose a {
    color: var(--primary);
  }

  .dark .prose strong {
    color: var(--foreground);
  }

  /* Dark mode list marker styling */
  .dark .prose ul > li::marker,
  .dark .prose ol > li::marker {
    color: var(--muted-foreground);
  }
}
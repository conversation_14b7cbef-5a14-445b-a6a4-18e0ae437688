import { useState, useEffect } from "react"
import { useSearchParams, useLocation } from "react-router-dom"
import { Plus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { PostFormDialog, PostsList, PostDialog } from "@/components/posts"
import { usePermissions } from "@/hooks/use-permissions"
import { DeepLinkUtils } from "@/lib/notification-navigation"
import { toast } from "sonner"

interface PostsTabProps {
  hubId: number
}

export function PostsTab({ hubId }: PostsTabProps) {
  const [searchParams, setSearchParams] = useSearchParams()
  const location = useLocation()
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [editingPostId, setEditingPostId] = useState<number | null>(null)
  const [isClosing, setIsClosing] = useState(false)
  const [closingPostId, setClosingPostId] = useState<number | null>(null)
  const { t, keys } = useTranslations()
  const { canCreatePost } = usePermissions()

  // URL is the single source of truth for dialog state
  const urlPostId = DeepLinkUtils.getPostToOpen(searchParams)
  const urlCommentId = DeepLinkUtils.getCommentToScrollTo(location.hash)
  const viewDialogOpen = !!urlPostId
  const scrollToComments = !!urlCommentId

  // Determine which post ID to show and whether dialog should be open
  const shouldShowDialog = viewDialogOpen || isClosing
  const dialogPostId = urlPostId || closingPostId

  // Handle dialog close with proper animation timing
  const handleViewDialogClose = (open: boolean) => {
    if (!open && urlPostId) {
      // Start closing animation
      setIsClosing(true)
      setClosingPostId(urlPostId)

      // Remove post parameter from URL immediately to prevent reopening
      const newParams = DeepLinkUtils.updateSearchParams(searchParams, {
        post: null,
      })
      setSearchParams(newParams, { replace: true })

      // Also clear any comment hash fragments
      if (location.hash.startsWith('#comment-')) {
        window.history.replaceState(null, '', window.location.pathname + window.location.search)
      }

      // Allow time for close animation to complete before unmounting
      setTimeout(() => {
        setIsClosing(false)
        setClosingPostId(null)
      }, 300) // Match the dialog animation duration
    }
  }








  const handleCreatePost = () => {
    if (!canCreatePost()) {
      toast.error("You don't have permission to create posts")
      return
    }
    setCreateDialogOpen(true)
  }

  const handleEditPost = (postId: number) => {
    setEditingPostId(postId)
    setEditDialogOpen(true)
  }

  const handleDialogSuccess = () => {
    // Show success message
    if (editingPostId) {
      toast.success(t(keys.collaborationHubs.posts.postUpdated))
    } else {
      toast.success(t(keys.collaborationHubs.posts.postCreated))
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header with create action */}
      <div className="flex justify-end mb-6">
        {canCreatePost() && (
          <Button onClick={handleCreatePost}>
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">{t(keys.collaborationHubs.posts.createPost)}</span>
          </Button>
        )}
      </div>

      {/* Posts Content */}
      <PostsList
        hubId={hubId}
        onEditPost={handleEditPost}
        className="flex-1"
        showFilters={true}
      />

      {/* Create Post Dialog */}
      <PostFormDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        hubId={hubId}
        onSuccess={handleDialogSuccess}
      />

      {/* Edit Post Dialog */}
      <PostFormDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        hubId={hubId}
        postId={editingPostId}
        onSuccess={handleDialogSuccess}
      />

      {/* View Post Dialog (for deep linking) */}
      {shouldShowDialog && dialogPostId && (
        <PostDialog
          postId={dialogPostId}
          hubId={hubId}
          open={viewDialogOpen}
          onOpenChange={handleViewDialogClose}
          onEdit={handleEditPost}
          scrollToComments={scrollToComments}
        />
      )}
    </div>
  )
}

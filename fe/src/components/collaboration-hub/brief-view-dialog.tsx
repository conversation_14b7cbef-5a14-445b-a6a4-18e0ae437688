import { BookOpen, Calendar, AlertCircle } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { useBrief } from '@/hooks/collaboration-hub-briefs';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface BriefViewDialogProps {
  briefId: number | null;
  hubId: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BriefViewDialog({ 
  briefId, 
  hubId, 
  open, 
  onOpenChange 
}: BriefViewDialogProps) {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();

  // Fetch brief details when dialog opens
  const {
    data: brief,
    isLoading,
    isError
  } = useBrief(hubId, briefId, {
    enabled: !!briefId && open
  });

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={cn(
          "max-w-4xl w-full h-[calc(100vh-2rem)] flex flex-col overflow-hidden",
          isMobile && "h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 m-0"
        )}
        aria-describedby="brief-dialog-description"
      >
        <DialogHeader className={cn(
          "flex-shrink-0",
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            "flex items-center gap-2",
            isMobile && "text-base"
          )}>
            <BookOpen className="h-5 w-5 text-primary" />
            {isLoading ? (
              <Skeleton className="h-6 w-48" />
            ) : (
              brief?.title || t(keys.collaborationHubs.briefs.viewDialog.title)
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 min-h-0">
          <ScrollArea className="h-full">
            <div id="brief-dialog-description" className={cn(
              "space-y-6 px-6 pb-4",
              isMobile && "px-4"
            )}>
            {/* Loading State */}
            {isLoading && (
              <div className="space-y-4">
                {/* Header skeleton */}
                <div className="flex items-center gap-3 pb-4 border-b">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-5 w-32 mb-1" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>

                {/* Content skeleton */}
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            )}

            {/* Error State */}
            {isError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {t(keys.collaborationHubs.briefs.viewDialog.error)}
                </AlertDescription>
              </Alert>
            )}

            {/* Content */}
            {brief && !isLoading && !isError && (
              <div className="space-y-6">
                {/* Header with creator info and timestamp */}
                <div className="flex items-center gap-3 pb-4 border-b">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="text-sm">
                      {getInitials(brief.createdByParticipantName || "Unknown")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="font-semibold">
                      {brief.createdByParticipantName || "Unknown"}
                    </p>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(brief.createdAt)}</span>
                      {brief.updatedAt !== brief.createdAt && (
                        <>
                          <span>•</span>
                          <span>{t(keys.collaborationHubs.briefs.updated)} {formatDate(brief.updatedAt)}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Brief content */}
                <div>
                  {brief.body ? (
                    <div className="prose prose-sm max-w-none dark:prose-invert">
                      <div
                        className="text-sm leading-relaxed"
                        dangerouslySetInnerHTML={{ __html: brief.body }}
                      />
                    </div>
                  ) : (
                    <p className="text-muted-foreground italic">
                      {t(keys.collaborationHubs.briefs.viewDialog.noContent)}
                    </p>
                  )}
                </div>
              </div>
            )}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
}

import React from "react"
import { UnifiedInput } from "@/components/ui/unified-input"

interface CommentFormProps {
  postId: number
  hubId: number
  onCommentCreated?: () => void
  className?: string
}

const CommentFormComponent = ({ postId, hubId, onCommentCreated, className }: CommentFormProps) => {
  return (
    <div className={className}>
      <UnifiedInput
        variant="comment"
        hubId={hubId}
        postId={postId}
        onSuccess={onCommentCreated}
      />
    </div>
  )
}

// Memoize the component to prevent unnecessary re-renders
export const CommentForm = React.memo(CommentFormComponent)

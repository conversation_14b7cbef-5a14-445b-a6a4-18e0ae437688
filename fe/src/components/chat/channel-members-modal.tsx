import React, { useCallback, useMemo } from 'react';
import { Users, Crown, Shield, User, Circle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useChannelMembers } from '@/hooks/chat/use-channel-members';
import { useIsMobile } from '@/hooks/use-mobile';
import { useChatUtils } from '@/hooks/chat';
import { cn } from '@/lib/utils';
import type { HubParticipantResponse } from '@/lib/types/api';

interface ChannelMembersModalProps {
  hubId: number;
  channelId: number;
  channelName: string;
  isOpen: boolean;
  onClose: () => void;
}

export const ChannelMembersModal = React.memo<ChannelMembersModalProps>(({
  hubId,
  channelId,
  channelName,
  isOpen,
  onClose
}) => {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();
  const { getInitials } = useChatUtils();

  const { data: membersResponse, isLoading, error } = useChannelMembers(
    hubId,
    channelId,
    { enabled: isOpen }
  );

  const members = useMemo(() => membersResponse?.content || [], [membersResponse?.content]);

  // Memoized utility functions
  const getRoleIcon = useCallback((role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return <Crown className="h-4 w-4 text-yellow-600" />;
      case 'reviewer':
        return <Shield className="h-4 w-4 text-blue-600" />;
      case 'content_creator':
        return <User className="h-4 w-4 text-green-600" />;
      default:
        return <User className="h-4 w-4 text-gray-600" />;
    }
  }, []);

  const getRoleBadgeColor = useCallback((role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      case 'reviewer':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'content_creator':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  }, []);

  const getOnlineStatus = useCallback((isOnline?: boolean) => {
    if (isOnline === undefined) return null;

    return (
      <div className="flex items-center gap-1">
        <Circle
          className={cn(
            "h-2 w-2 fill-current",
            isOnline ? "text-green-500" : "text-gray-400"
          )}
        />
        <span className="text-xs text-muted-foreground">
          {isOnline ? 'Online' : 'Offline'}
        </span>
      </div>
    );
  }, []);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn(
        "max-w-md",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 m-0"
      )}>
        <DialogHeader className={cn(
          "pb-4",
          isMobile && "pb-2 pt-6 px-6"
        )}>
          <DialogTitle className={cn(
            "flex items-center gap-3 text-lg",
            isMobile && "text-base gap-2"
          )}>
            <Users className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
            {channelName} {t(keys.collaborationHubs.chat.members)}
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className={cn(
          "max-h-96 px-6",
          isMobile && "h-[calc(100dvh-120px)] px-6"
        )}>
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex items-center gap-3 p-2">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex-1 space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                  <Skeleton className="h-5 w-16" />
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">
                {t(keys.collaborationHubs.chat.failedToLoadMembers)}
              </p>
            </div>
          ) : members.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">
                {t(keys.collaborationHubs.chat.noMembers)}
              </p>
            </div>
          ) : (
            <div className={cn("space-y-2", isMobile && "space-y-3")}>
              {members.map((member: HubParticipantResponse) => (
                <div
                  key={member.id}
                  className={cn(
                    "flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors",
                    isMobile && "p-4 min-h-[60px]"
                  )}
                >
                  <Avatar className={cn("h-10 w-10", isMobile && "h-12 w-12")}>
                    <AvatarFallback className={cn("text-sm font-medium", isMobile && "text-base")}>
                      {getInitials(member.name || '')}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      {getRoleIcon(member.role)}
                      <p className={cn("font-medium text-sm truncate", isMobile && "text-base")}>
                        {member.name}
                      </p>
                      {member.isExternal && (
                        <Badge variant="outline" className={cn("text-xs", isMobile && "text-sm")}>
                          External
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-2 mt-1">
                      <p className={cn("text-xs text-muted-foreground truncate", isMobile && "text-sm")}>
                        {member.email}
                      </p>
                      {!isMobile && getOnlineStatus(false)} {/* Hide online status on mobile for space */}
                    </div>
                  </div>

                  <Badge className={cn(getRoleBadgeColor(member.role), isMobile && "text-sm px-3 py-1")}>
                    {member.role.replace('_', ' ')}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {!isLoading && !error && members.length > 0 && (
          <div className={cn("pt-3 border-t", isMobile && "pt-4 pb-6 px-6")}>
            <p className={cn("text-sm text-muted-foreground text-center", isMobile && "text-base")}>
              {members.length} {members.length === 1 ? 'member' : 'members'}
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
});

ChannelMembersModal.displayName = 'ChannelMembersModal';

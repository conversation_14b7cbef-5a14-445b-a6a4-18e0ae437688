import React, { useCallback } from 'react';
import { Download, FileText, Image, Video } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useDownloadAttachment } from '@/hooks/chat/use-download-attachment';
import { useChatUtils } from '@/hooks/chat';

interface Attachment {
  url: string;
  filename: string;
  content_type?: string;
  size: number;
  type?: string;
}

interface MessageAttachmentsProps {
  hubId: number;
  attachments: Attachment[];
}

export const MessageAttachments = React.memo<MessageAttachmentsProps>(({ hubId, attachments }) => {
  const { downloadAttachment } = useDownloadAttachment(hubId);
  const { formatFileSize } = useChatUtils();

  const getFileIcon = useCallback((contentType: string | null | undefined) => {
    if (!contentType) return <FileText className="h-4 w-4" />;
    if (contentType.startsWith('image/')) return <Image className="h-4 w-4" />;
    if (contentType.startsWith('video/')) return <Video className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  }, []);

  const handleDownload = useCallback(async (attachment: { url: string; filename: string }) => {
    try {
      await downloadAttachment(attachment.url, attachment.filename);
    } catch (__error) {
      // Error handling is done in the hook
    }
  }, [downloadAttachment]);

  if (!attachments || attachments.length === 0) {
    return null;
  }

  return (
    <div className="space-y-2 mt-2">
      {attachments.map((attachment, index) => (
        <div key={index} className="flex items-center gap-2 p-2 border rounded-lg bg-muted/30">
          {getFileIcon(attachment.content_type || 'application/octet-stream')}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{attachment.filename}</p>
            <p className="text-xs text-muted-foreground">
              {formatFileSize(attachment.size)}
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleDownload(attachment);
            }}
          >
            <Download className="h-4 w-4" />
          </Button>
        </div>
      ))}
    </div>
  );
});

MessageAttachments.displayName = 'MessageAttachments';

import { $api } from '@/lib/api/client';
import type { HubParticipantResponse } from '@/lib/types/api';

/**
 * Custom hook for fetching channel members/participants.
 * Fetches the specific chat channel details and extracts the participants
 * who are actual members of that channel.
 *
 * The backend automatically:
 * - Scopes participants to the current account (multi-tenancy)
 * - Validates user access to the hub and channel
 * - Returns only participants who are members of the specific channel
 * - Handles both general channels (all hub participants) and custom channels (selected participants)
 *
 * @param hubId - Hub ID
 * @param channelId - Channel ID
 * @param options - Query options including enabled and staleTime
 */
export function useChannelMembers(
  hubId: number,
  channelId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  const channelQuery = $api.useQuery('get', '/api/hubs/{hubId}/chats/{channelId}', {
    params: {
      path: { hubId, channelId },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId && !!channelId,
    // Cache data for 1 minute (participant list changes less frequently)
    staleTime: options?.staleTime ?? 1 * 60 * 1000,
    // Enable automatic refetching on window focus for fresh status
    refetchOnWindowFocus: true,
  });

  // Transform the response to match the expected format
  // Extract participants from the channel response and transform ChatParticipantDto to HubParticipantResponse
  const transformedData = channelQuery.data ? {
    content: (channelQuery.data.participants || []).map((participant): HubParticipantResponse => ({
      id: participant.id!,
      userId: undefined, // ChatParticipantDto doesn't include userId
      email: participant.email || '',
      name: participant.name || '',
      role: participant.role as any, // Role enums should be compatible
      isExternal: participant.is_external || false,
      invitedAt: new Date().toISOString(), // Default value since ChatParticipantDto doesn't include this
      joinedAt: new Date().toISOString(), // Default value since ChatParticipantDto doesn't include this
      status: 'active' // Default to active since they're channel members
    })),
    totalElements: channelQuery.data.participant_count || 0,
    totalPages: 1,
    size: channelQuery.data.participants?.length || 0,
    number: 0,
    numberOfElements: channelQuery.data.participants?.length || 0,
    first: true,
    last: true,
    empty: (channelQuery.data.participants?.length || 0) === 0
  } : undefined;

  return {
    ...channelQuery,
    data: transformedData
  };
}

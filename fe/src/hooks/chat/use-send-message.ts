import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from '@/lib/i18n/typed-translations';

/**
 * Custom hook for sending chat messages.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the messages query on success to show the new message.
 * Also updates the channel list to reflect the latest message.
 * 
 * The backend automatically:
 * - Validates user permissions to write to the channel
 * - Parses mentions from message content
 * - <PERSON>les file attachments
 * - Broadcasts the message via WebSocket to other participants
 * - Sends notifications for mentions
 * 
 */
export function useSendMessage() {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('post', '/api/chats/{channelId}/messages', {
    onSuccess: (_, variables) => {
      const channelId = variables.params.path.channelId;

      // Invalidate and refetch messages for this channel (match the useChatMessages key)
      queryClient.invalidateQueries({
        predicate: (query) => {
          const k = query.queryKey as unknown[];
          return k?.[0] === 'get' && k?.[1] === '/api/chats/{channelId}/messages' && (k?.[2] as any)?.channelId === channelId;
        }
      });

      // Invalidate channel list to update last message and unread counts
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/hubs/{hubId}/chats';
        }
      });

      // Optionally, we could optimistically update the messages cache here
      // but since we have WebSocket updates, we'll rely on those for real-time updates
    },
    onError: (error) => {
      console.error('Failed to send message:', error);
      toast.error(t(keys.collaborationHubs.chat.messageError));
    },
  });
}

/**
 * Custom hook for updating/editing chat messages.
 * 
 */
export function useUpdateMessage() {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('put', '/api/chats/{channelId}/messages/{messageId}', {
    onSuccess: (_, variables) => {
      const channelId = variables.params.path.channelId;

      // Invalidate messages to show the updated message (match the useChatMessages key)
      queryClient.invalidateQueries({
        predicate: (query) => {
          const k = query.queryKey as unknown[];
          return k?.[0] === 'get' && k?.[1] === '/api/chats/{channelId}/messages' && (k?.[2] as any)?.channelId === channelId;
        }
      });

      // Show success toast
      toast.success(t(keys.collaborationHubs.chat.messageUpdated));
    },
    onError: (error) => {
      console.error('Failed to update message:', error);
      toast.error(t(keys.collaborationHubs.chat.messageUpdateError));
    },
  });
}

/**
 * Custom hook for deleting chat messages.
 */
export function useDeleteMessage() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/chats/{channelId}/messages/{messageId}', {
    onSuccess: (_, variables) => {
      const channelId = variables.params.path.channelId;

      // Invalidate messages to remove the deleted message (match the useChatMessages key)
      queryClient.invalidateQueries({
        predicate: (query) => {
          const k = query.queryKey as unknown[];
          return k?.[0] === 'get' && k?.[1] === '/api/chats/{channelId}/messages' && (k?.[2] as any)?.channelId === channelId;
        }
      });

      // Invalidate channel list in case this was the last message
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/hubs/{hubId}/chats';
        }
      });
    },
    onError: (__error) => {
      // Error handling for message deletion
    },
  });
}

import { useAuth } from '@/contexts/auth-context';

export function useDownloadAttachment(hubId: number) {
  const { accessToken } = useAuth();

  const parseFilenameFromContentDisposition = (cd: string | null): string | null => {
    if (!cd) return null;

    // RFC 5987: filename*=UTF-8''...
    const starMatch = cd.match(/filename\*=(?:UTF-8''|)([^;]+)/i);
    if (starMatch?.[1]) {
      try {
        return decodeURIComponent(starMatch[1].trim().replace(/"/g, ''));
      } catch {
        /* ignore decode failure */
      }
    }

    // Basic filename="..." fallback
    const match = cd.match(/filename="?([^";]+)"?/i);
    return match?.[1]?.trim() ?? null;
  };

  const inferFilenameFromUrl = (url: string): string => {
    try {
      const { pathname } = new URL(url);
      const last = pathname.substring(pathname.lastIndexOf('/') + 1);
      return last || 'download';
    } catch {
      return 'download';
    }
  };

  const downloadAttachment = async (
    fileUrl: string,
    filename?: string,
    signal?: AbortSignal
  ): Promise<void> => {
    if (!hubId || hubId <= 0) {
      throw new Error('Invalid hubId for download');
    }

    try {
      const response = await fetch(`/api/hubs/${hubId}/media/download-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: new URLSearchParams({ fileUrl }),
        signal,
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData.error?.message) errorMessage = errorData.error.message;
        } catch {
          // ignore JSON parse errors
        }
        throw new Error(errorMessage);
      }

      const { downloadUrl: presignedUrl } = await response.json() as { downloadUrl: string };

      // Fetch the actual file
      const fileResp = await fetch(presignedUrl, { method: 'GET', credentials: 'omit', signal });
      if (!fileResp.ok) {
        throw new Error(`Failed to fetch file: ${fileResp.status} ${fileResp.statusText}`);
      }

      const blob = await fileResp.blob();

      // Pick filename (priority: Content-Disposition > argument > URL inference)
      const cd = fileResp.headers.get('content-disposition');
      const headerFilename = parseFilenameFromContentDisposition(cd);
      const finalName = headerFilename || filename || inferFilenameFromUrl(presignedUrl);

      // Create object URL & download
      const objectUrl = window.URL.createObjectURL(blob);
      try {
        const link = document.createElement('a');
        link.href = objectUrl;
        link.download = finalName;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } finally {
        // Delay revoke slightly for safety
        setTimeout(() => window.URL.revokeObjectURL(objectUrl), 1000);
      }
    } catch (error) {
      if ((error as any).name === 'AbortError') {
        console.warn('Download aborted by user');
        return;
      }
      console.error('Failed to download attachment:', error);
      throw error;
    }
  };

  return { downloadAttachment };
}
